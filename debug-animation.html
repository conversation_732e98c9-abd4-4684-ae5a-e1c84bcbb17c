<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الأنيميشن</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-button {
            background: #6a3de8;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            display: block;
            width: 300px;
        }
        
        /* Copy exact CSS from main file */
        .rid-cod-form-classic.animate-button #rid-cod-submit-btn:not(.atc-buy-button) {
            animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite !important;
            transform: translate3d(0, 0, 0);
            perspective: 1000px;
            will-change: transform;
        }

        @keyframes shaking {
            0%, 100% {
                transform: translate3d(0, 0, 0);
            }
            4%, 46% {
                transform: translate3d(-1px, 0, 0);
            }
            8%, 42% {
                transform: translate3d(2px, 0, 0);
            }
            12%, 37% {
                transform: translate3d(-3px, 0, 0);
            }
            16%, 33% {
                transform: translate3d(3px, 0, 0);
            }
            20%, 29% {
                transform: translate3d(-2px, 0, 0);
            }
            24%, 25% {
                transform: translate3d(1px, 0, 0);
            }
        }
        
        /* Test simple animation */
        .simple-shake {
            animation: simpleShake 1s infinite;
        }
        
        @keyframes simpleShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }
        
        .debug-info {
            background: white;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .status {
            font-weight: bold;
            margin: 5px 0;
        }
        
        .working { color: green; }
        .not-working { color: red; }
    </style>
</head>
<body>
    <h1>تشخيص مشكلة الأنيميشن</h1>
    
    <div class="debug-info">
        <h3>معلومات الجهاز:</h3>
        <div class="status">عرض الشاشة: <span id="screen-width"></span>px</div>
        <div class="status">نوع الجهاز: <span id="device-type"></span></div>
        <div class="status">دعم CSS Animation: <span id="css-support"></span></div>
    </div>
    
    <h3>اختبار 1: أنيميشن بسيط</h3>
    <button class="test-button simple-shake" id="simple-test">زر بأنيميشن بسيط</button>
    <div class="status" id="simple-status">حالة الأنيميشن البسيط: <span class="not-working">غير محدد</span></div>
    
    <h3>اختبار 2: أنيميشن RID COD (بدون كلاسات)</h3>
    <button class="test-button" id="direct-test" style="animation: shaking 1.8s infinite;">زر بأنيميشن مباشر</button>
    <div class="status" id="direct-status">حالة الأنيميشن المباشر: <span class="not-working">غير محدد</span></div>
    
    <h3>اختبار 3: أنيميشن RID COD (مع الكلاسات)</h3>
    <div class="rid-cod-form-classic animate-button">
        <button class="test-button" id="rid-cod-submit-btn">زر RID COD مع الكلاسات</button>
    </div>
    <div class="status" id="ridcod-status">حالة أنيميشن RID COD: <span class="not-working">غير محدد</span></div>
    
    <div class="debug-info">
        <h3>خطوات التشخيص:</h3>
        <ol>
            <li>إذا كان الاختبار 1 يعمل والباقي لا يعمل = مشكلة في CSS keyframes</li>
            <li>إذا كان الاختبار 2 يعمل والاختبار 3 لا يعمل = مشكلة في الكلاسات</li>
            <li>إذا كان جميع الاختبارات لا تعمل = مشكلة في دعم CSS Animation</li>
        </ol>
    </div>

    <script>
        // Display device info
        function updateDeviceInfo() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width;
            
            let deviceType = '';
            if (width >= 769) {
                deviceType = 'سطح المكتب';
            } else if (width >= 481) {
                deviceType = 'جهاز لوحي';
            } else {
                deviceType = 'هاتف محمول';
            }
            document.getElementById('device-type').textContent = deviceType;
            
            // Check CSS animation support
            const testEl = document.createElement('div');
            testEl.style.animation = 'test 1s';
            const hasSupport = testEl.style.animation !== '';
            document.getElementById('css-support').textContent = hasSupport ? 'مدعوم' : 'غير مدعوم';
            document.getElementById('css-support').className = hasSupport ? 'working' : 'not-working';
        }
        
        // Check if animations are working
        function checkAnimations() {
            setTimeout(() => {
                // Check simple animation
                const simpleEl = document.getElementById('simple-test');
                const simpleComputed = window.getComputedStyle(simpleEl);
                const simpleWorking = simpleComputed.animationName !== 'none';
                document.getElementById('simple-status').innerHTML = 
                    'حالة الأنيميشن البسيط: <span class="' + (simpleWorking ? 'working' : 'not-working') + '">' + 
                    (simpleWorking ? 'يعمل' : 'لا يعمل') + '</span>';
                
                // Check direct animation
                const directEl = document.getElementById('direct-test');
                const directComputed = window.getComputedStyle(directEl);
                const directWorking = directComputed.animationName !== 'none';
                document.getElementById('direct-status').innerHTML = 
                    'حالة الأنيميشن المباشر: <span class="' + (directWorking ? 'working' : 'not-working') + '">' + 
                    (directWorking ? 'يعمل' : 'لا يعمل') + '</span>';
                
                // Check RID COD animation
                const ridcodEl = document.getElementById('rid-cod-submit-btn');
                const ridcodComputed = window.getComputedStyle(ridcodEl);
                const ridcodWorking = ridcodComputed.animationName !== 'none';
                document.getElementById('ridcod-status').innerHTML = 
                    'حالة أنيميشن RID COD: <span class="' + (ridcodWorking ? 'working' : 'not-working') + '">' + 
                    (ridcodWorking ? 'يعمل' : 'لا يعمل') + '</span>';
                
                console.log('Simple animation:', simpleComputed.animationName);
                console.log('Direct animation:', directComputed.animationName);
                console.log('RID COD animation:', ridcodComputed.animationName);
            }, 1000);
        }
        
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        checkAnimations();
    </script>
</body>
</html>
