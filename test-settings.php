<?php
// Test file to check RID COD settings
// Add this to WordPress to test

// Make sure WordPress is loaded
if (!defined('ABSPATH')) {
    // If running standalone, try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
    
    if (!defined('ABSPATH')) {
        die('WordPress not found. Please run this file from WordPress admin or copy the code to a WordPress page.');
    }
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعدادات RID COD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
        }
        .setting-row {
            padding: 10px;
            margin: 5px 0;
            background: #f9f9f9;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
        }
        .setting-name {
            font-weight: bold;
        }
        .setting-value {
            color: #666;
        }
        .enabled {
            color: green;
            font-weight: bold;
        }
        .disabled {
            color: red;
        }
        .test-form {
            margin-top: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار إعدادات RID COD</h1>
        
        <h2>الإعدادات الحالية:</h2>
        
        <?php
        // Get all RID COD related settings
        $settings = [
            'rid_cod_form_style' => 'شكل النموذج',
            'rid_cod_second_form_animate_button' => 'تفعيل أنيميشن الزر',
            'rid_cod_button_text' => 'نص الزر',
        ];
        
        foreach ($settings as $setting_key => $setting_name) {
            $value = get_option($setting_key, 'غير محدد');
            $display_value = $value;
            
            if ($setting_key === 'rid_cod_second_form_animate_button') {
                $display_value = ($value === '1') ? 'مفعل' : 'غير مفعل';
                $class = ($value === '1') ? 'enabled' : 'disabled';
            } else {
                $class = 'setting-value';
            }
            
            echo '<div class="setting-row">';
            echo '<span class="setting-name">' . esc_html($setting_name) . ':</span>';
            echo '<span class="' . $class . '">' . esc_html($display_value) . '</span>';
            echo '</div>';
        }
        ?>
        
        <h2>اختبار تطبيق الكلاسات:</h2>
        
        <?php
        // Simulate the form class generation
        $form_style = get_option('rid_cod_form_style', 'classic');
        $animate_button = get_option('rid_cod_second_form_animate_button', '1');
        
        $form_classes = [];
        
        if ($form_style === 'modern') {
            $form_classes[] = 'rid-cod-form-modern';
        } elseif ($form_style === 'second') {
            $form_classes[] = 'rid-cod-form-second';
        } elseif ($form_style === 'third') {
            $form_classes[] = 'rid-cod-form-third';
        } else {
            $form_classes[] = 'rid-cod-form-classic';
        }
        
        if ($animate_button === '1') {
            $form_classes[] = 'animate-button';
        }
        
        $final_class = implode(' ', $form_classes);
        ?>
        
        <div class="setting-row">
            <span class="setting-name">كلاسات النموذج المطبقة:</span>
            <span class="setting-value"><?php echo esc_html($final_class); ?></span>
        </div>
        
        <div class="test-form">
            <h3>معاينة النموذج:</h3>
            <div class="<?php echo esc_attr($final_class); ?>">
                <p><strong>كلاس النموذج:</strong> <?php echo esc_html($final_class); ?></p>
                
                <?php if ($form_style === 'third'): ?>
                    <button id="rid-cod-submit-btn-third" style="background: #6a3de8; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer;">
                        <?php echo esc_html(get_option('rid_cod_button_text', 'اطلب الآن')); ?>
                    </button>
                <?php else: ?>
                    <button id="rid-cod-submit-btn" style="background: #6a3de8; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer;">
                        <?php echo esc_html(get_option('rid_cod_button_text', 'انقر هنا لتأكيد الطلب')); ?>
                    </button>
                <?php endif; ?>
                
                <p><strong>حالة الأنيميشن:</strong> 
                    <span class="<?php echo ($animate_button === '1') ? 'enabled' : 'disabled'; ?>">
                        <?php echo ($animate_button === '1') ? 'مفعل' : 'غير مفعل'; ?>
                    </span>
                </p>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 8px;">
            <h3>ملاحظات:</h3>
            <ul>
                <li>إذا كان الأنيميشن مفعل ولكن لا يعمل، تحقق من ملف CSS</li>
                <li>إذا كانت الكلاسات صحيحة ولكن الأنيميشن لا يعمل، تحقق من تحميل ملف CSS</li>
                <li>تأكد من أن المتصفح يدعم CSS animations</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px;">
            <a href="<?php echo admin_url('admin.php?page=rid_cod_settings'); ?>" style="background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
                الذهاب إلى إعدادات RID COD
            </a>
        </div>
    </div>
    
    <!-- Include the actual CSS file to test animation -->
    <link rel="stylesheet" href="<?php echo plugin_dir_url(__FILE__) . 'assets/css/rid-cod.css'; ?>">
    
    <script>
        // Check if animation is actually applied
        setTimeout(() => {
            const buttons = document.querySelectorAll('#rid-cod-submit-btn, #rid-cod-submit-btn-third');
            buttons.forEach(button => {
                const computed = window.getComputedStyle(button);
                console.log('Button ID:', button.id);
                console.log('Animation Name:', computed.animationName);
                console.log('Animation Duration:', computed.animationDuration);
                console.log('Parent Classes:', button.parentElement.className);
            });
        }, 1000);
    </script>
</body>
</html>
