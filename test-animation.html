<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أنيميشن الأزرار - RID COD</title>
    <style>
        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-test {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .form-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #6a3de8;
        }
        
        /* Copy the animation CSS from the main file - Updated */
        .rid-cod-form-classic.animate-button #rid-cod-submit-btn:not(.atc-buy-button),
        .rid-cod-form-modern.animate-button #rid-cod-submit-btn:not(.atc-buy-button),
        .rid-cod-form-second.animate-button #rid-cod-submit-btn:not(.atc-buy-button),
        .rid-cod-form-third.animate-button #rid-cod-submit-btn-third:not(.atc-buy-button),
        .rid-cod-form-third.animate-button .rid-cod-submit-btn-third:not(.atc-buy-button) {
            animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite !important;
            transform: translate3d(0, 0, 0) !important;
            perspective: 1000px;
            will-change: transform;
        }

        /* Fallback animation rule */
        .animate-button #rid-cod-submit-btn,
        .animate-button #rid-cod-submit-btn-third {
            animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite !important;
            transform: translate3d(0, 0, 0) !important;
        }

        @keyframes shaking {
            0%, 100% {
                transform: translate3d(0, 0, 0);
            }
            4%, 46% {
                transform: translate3d(-1px, 0, 0);
            }
            8%, 42% {
                transform: translate3d(2px, 0, 0);
            }
            12%, 37% {
                transform: translate3d(-3px, 0, 0);
            }
            16%, 33% {
                transform: translate3d(3px, 0, 0);
            }
            20%, 29% {
                transform: translate3d(-2px, 0, 0);
            }
            24%, 25% {
                transform: translate3d(1px, 0, 0);
            }
        }
        
        /* Button styles */
        .test-button {
            background: #6a3de8;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #5a2dd8;
            box-shadow: 0 4px 15px rgba(106, 61, 232, 0.3);
        }
        
        .toggle-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        
        .toggle-btn:hover {
            background: #218838;
        }
        
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status.active {
            color: #28a745;
        }
        
        .status.inactive {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار أنيميشن أزرار تأكيد الطلب</h1>
        
        <!-- Classic Form Test -->
        <div class="form-test rid-cod-form-classic" id="classic-test">
            <div class="form-title">النموذج الكلاسيكي</div>
            <div class="status inactive" id="classic-status">الأنيميشن: غير مفعل</div>
            <button class="toggle-btn" onclick="toggleAnimation('classic')">تفعيل/إلغاء الأنيميشن</button>
            <br>
            <button class="test-button" id="rid-cod-submit-btn">انقر هنا لتأكيد الطلب</button>
        </div>
        
        <!-- Modern Form Test -->
        <div class="form-test rid-cod-form-modern" id="modern-test">
            <div class="form-title">النموذج الحديث</div>
            <div class="status inactive" id="modern-status">الأنيميشن: غير مفعل</div>
            <button class="toggle-btn" onclick="toggleAnimation('modern')">تفعيل/إلغاء الأنيميشن</button>
            <br>
            <button class="test-button" id="rid-cod-submit-btn">انقر هنا لتأكيد الطلب</button>
        </div>
        
        <!-- Second Form Test -->
        <div class="form-test rid-cod-form-second" id="second-test">
            <div class="form-title">النموذج الثاني</div>
            <div class="status inactive" id="second-status">الأنيميشن: غير مفعل</div>
            <button class="toggle-btn" onclick="toggleAnimation('second')">تفعيل/إلغاء الأنيميشن</button>
            <br>
            <button class="test-button" id="rid-cod-submit-btn">انقر هنا لتأكيد الطلب</button>
        </div>
        
        <!-- Third Form Test -->
        <div class="form-test rid-cod-form-third" id="third-test">
            <div class="form-title">النموذج الثالث</div>
            <div class="status inactive" id="third-status">الأنيميشن: غير مفعل</div>
            <button class="toggle-btn" onclick="toggleAnimation('third')">تفعيل/إلغاء الأنيميشن</button>
            <br>
            <button class="test-button" id="rid-cod-submit-btn-third">اطلب الآن</button>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f4fd; border-radius: 8px;">
            <h3>ملاحظات الاختبار:</h3>
            <ul>
                <li>يجب أن تعمل الأنيميشن على جميع النماذج عند التفعيل</li>
                <li>الأنيميشن عبارة عن اهتزاز خفيف للزر لجذب الانتباه</li>
                <li><strong>أجهزة سطح المكتب (769px+):</strong> أنيميشن كامل</li>
                <li><strong>الأجهزة اللوحية (481px-768px):</strong> أنيميشن متوسط</li>
                <li><strong>الهواتف المحمولة (480px وأقل):</strong> أنيميشن خفيف</li>
                <li>المستخدمون الذين يفضلون تقليل الحركة لن يروا الأنيميشن</li>
            </ul>
            <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px;">
                <strong>حجم الشاشة الحالي:</strong> <span id="screen-size"></span>
            </div>
        </div>
    </div>

    <script>
        function toggleAnimation(formType) {
            const testElement = document.getElementById(formType + '-test');
            const statusElement = document.getElementById(formType + '-status');
            
            if (testElement.classList.contains('animate-button')) {
                testElement.classList.remove('animate-button');
                statusElement.textContent = 'الأنيميشن: غير مفعل';
                statusElement.className = 'status inactive';
            } else {
                testElement.classList.add('animate-button');
                statusElement.textContent = 'الأنيميشن: مفعل';
                statusElement.className = 'status active';
            }
        }
        
        // Display screen size
        function updateScreenSize() {
            const width = window.innerWidth;
            let deviceType = '';

            if (width >= 769) {
                deviceType = 'سطح المكتب (أنيميشن كامل)';
            } else if (width >= 481) {
                deviceType = 'جهاز لوحي (أنيميشن متوسط)';
            } else {
                deviceType = 'هاتف محمول (أنيميشن خفيف)';
            }

            document.getElementById('screen-size').textContent = width + 'px - ' + deviceType;
        }

        // Update screen size on load and resize
        updateScreenSize();
        window.addEventListener('resize', updateScreenSize);

        // Debug function to check animation status
        function debugAnimation() {
            ['classic', 'modern', 'second', 'third'].forEach(formType => {
                const container = document.getElementById(formType + '-test');
                const button = container.querySelector('.test-button');
                const computed = window.getComputedStyle(button);

                console.log(`${formType} Form:`);
                console.log('- Container classes:', container.className);
                console.log('- Button ID:', button.id);
                console.log('- Animation name:', computed.animationName);
                console.log('- Animation duration:', computed.animationDuration);
                console.log('- Transform:', computed.transform);
                console.log('---');
            });
        }

        // Auto-enable animation for demonstration
        setTimeout(() => {
            ['classic', 'modern', 'second', 'third'].forEach(formType => {
                toggleAnimation(formType);
            });

            // Debug after enabling
            setTimeout(debugAnimation, 500);
        }, 1000);
    </script>
</body>
</html>
